import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    GESS: require('../assets/fonts/GESS.ttf'),
    GESSTwo: require('../assets/fonts/GESSTwo.ttf'),
    GESSTwoLight: require('../assets/fonts/GESSTwoLight.ttf'),
    GESSTwoLightGG: require('../assets/fonts/GESSTwoLightGG.ttf'),
    GESSTwoLightOne: require('../assets/fonts/GESSTwoLightOne.ttf'),
    GESSTwoLightTwo: require('../assets/fonts/GESSTwoLightTwo.ttf'),
    GE_SS_Two_Bold: require('../assets/fonts/GE_SS_Two_Bold.otf'),
    rabi3: require('../assets/fonts/rabi3.ttf'),
    RubikBold: require('../assets/fonts/Rubik-Bold.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack>
        <Stack.Screen name="(drawer)" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}