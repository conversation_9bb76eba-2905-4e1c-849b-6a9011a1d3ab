// /app/(drawer)/_layout.tsx
import { Ionicons } from '@expo/vector-icons';
import { Drawer } from 'expo-router/drawer';

import { CustomDrawerContent } from '@/components/CustomDrawerContent';
import { CustomHeader } from '@/components/CustomHeader';
import { Colors } from '@/constants/Colors';

export default function DrawerLayout() {
  return (
    <Drawer
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        header: () => <CustomHeader />,
        headerShown: true,
        drawerActiveTintColor: Colors.dark.tint,
        drawerInactiveTintColor: Colors.dark.icon,
        drawerActiveBackgroundColor: '#007BFF30',
        drawerLabelStyle: {
          fontFamily: 'GESSTwo',
          fontSize: 16,
          textAlign: 'left',
          marginLeft: -20,
        },
        drawerStyle: {
          backgroundColor: Colors.dark.background,
          width: 280,
        },
      }}>
      {/*
        This is the fix. We manually define each screen for the drawer.
        The `name` prop points to the screen's file name inside the `(tabs)` folder.
      */}
      <Drawer.Screen
        name="index" // This points to /app/(drawer)/(tabs)/index.tsx
        options={{
          drawerLabel: 'الرئيسية',
          title: 'الرئيسية',
          drawerIcon: ({ color, size }) => <Ionicons name="home-outline" size={size} color={color} />,
        }}
      />
      <Drawer.Screen
        name="news" // This points to /app/(drawer)/(tabs)/news.tsx
        options={{
          drawerLabel: 'الاخبار',
          title: 'الاخبار',
          drawerIcon: ({ color, size }) => <Ionicons name="newspaper-outline" size={size} color={color} />,
        }}
      />
      <Drawer.Screen
        name="register" // This points to /app/(drawer)/(tabs)/register.tsx
        options={{
          drawerLabel: 'التسجيل',
          title: 'التسجيل',
          drawerIcon: ({ color, size }) => <Ionicons name="person-add-outline" size={size} color={color} />,
        }}
      />
    </Drawer>
  );
}