// components/CustomHeader.tsx
import { BlurView } from 'expo-blur';
import { Image } from 'expo-image';
import { StyleSheet, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';

export function CustomHeader() {
  const { top } = useSafeAreaInsets();
  return (
    <BlurView
      intensity={100}
      tint="light"
      style={[styles.container, { paddingTop: top }]}
    >
      <View style={styles.content}>
        <Image
          source={require('@/assets/images/menu-icon.png')} // Assuming you have a menu icon
          style={styles.menuIcon}
        />
        <View style={styles.logoContainer}>
          <ThemedText style={styles.title}>وليد زيتون</ThemedText>
          <Image
            source={require('@/assets/images/logo.png')} // Assuming you have a logo
            style={styles.logo}
          />
        </View>
      </View>
    </BlurView>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    height: 60,
  },
  menuIcon: {
    width: 24,
    height: 24,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    width: 40,
    height: 40,
    marginLeft: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
});