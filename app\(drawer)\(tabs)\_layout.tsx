// /app/(drawer)/(tabs)/_layout.tsx
import { Ionicons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import React from 'react';
import { Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function TabLayout() {
  const colorScheme = useColorScheme();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            position: 'absolute',
          },
          default: {},
        }),
        tabBarLabelStyle: {
          fontFamily: 'GESSTwo',
          fontSize: 12,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'الرئيسية',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="house.fill" color={color} />,
          // drawerIcon and drawerLabel are now defined in the parent drawer layout
        }}
      />
      <Tabs.Screen
        name="news"
        options={{
          title: 'الاخبار',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="newspaper" color={color} />,
          // drawerIcon and drawerLabel are now defined in the parent drawer layout
        }}
      />
      <Tabs.Screen
        name="register"
        options={{
          title: 'التسجيل',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person" color={color} />,
          // drawerIcon and drawerLabel are now defined in the parent drawer layout
        }}
      />
    </Tabs>
  );
}